# 🎮 Tesla Shop - Gaming Cards & Digital Services Platform

<div align="center">

![Tesla Shop](https://img.shields.io/badge/Tesla%20Shop-Gaming%20Platform-red?style=for-the-badge&logo=tesla)
![Next.js](https://img.shields.io/badge/Next.js-15.2.4-black?style=for-the-badge&logo=next.js)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)
![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3.4-38B2AC?style=for-the-badge&logo=tailwind-css)

**A modern, Arabic-first gaming cards and digital services platform built with Next.js 15**

[🚀 Live Demo](https://tesla-shop.vercel.app) • [📖 Documentation](#documentation) • [🐛 Report Bug](https://github.com/altyb/tesla-shop/issues) • [✨ Request Feature](https://github.com/altyb/tesla-shop/issues)

</div>

---

## 🌟 Features

### 🎯 **Core Features**
- **🎮 Gaming Cards Store** - Purchase gems, memberships, and battle passes
- **💰 Digital Wallet System** - Secure balance management and transactions
- **👤 User Management** - Profile management with Arabic support
- **🛡️ Admin Dashboard** - Comprehensive admin panel for platform management
- **📱 Mobile-First Design** - Responsive design optimized for all devices

### 🎨 **User Experience**
- **🌙 Dark Theme** - Modern dark UI with red accent colors
- **🔄 Real-time Updates** - Live order status and balance updates
- **📋 Copy Utilities** - Easy-to-use copy buttons for IDs and codes
- **🎭 Smooth Animations** - Tailwind CSS animations and transitions
- **🌐 Arabic RTL Support** - Full right-to-left language support

### 🔧 **Technical Features**
- **⚡ Next.js 15** - Latest React framework with App Router
- **🎨 Radix UI** - Accessible, unstyled UI components
- **🎯 TypeScript** - Full type safety and developer experience
- **🎪 Tailwind CSS** - Utility-first CSS framework
- **📊 Context API** - State management for user data and orders
- **🔒 Security Headers** - Built-in security optimizations

---

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm, yarn, or pnpm

### Installation

```bash
# Clone the repository
git clone https://github.com/altyb/tesla-shop.git
cd tesla-shop

# Install dependencies
npm install
# or
yarn install
# or
pnpm install

# Start development server
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

---

## 📁 Project Structure

```
tesla-shop/
├── app/                          # Next.js App Router
│   ├── admin/                    # Admin dashboard
│   │   ├── components/           # Admin-specific components
│   │   │   ├── AdminOverview.tsx # Dashboard overview
│   │   │   ├── UserManagement.tsx# User management panel
│   │   │   ├── OrderManagement.tsx# Order processing
│   │   │   ├── ProductManagement.tsx# Product catalog
│   │   │   └── SystemSettings.tsx# System configuration
│   │   └── page.tsx             # Admin main page
│   ├── components/              # Shared components
│   │   ├── CopyButton.tsx       # Copy-to-clipboard utility
│   │   ├── Navbar.tsx           # Navigation component
│   │   ├── OfferCards.tsx       # Gaming offers display
│   │   ├── PlayerIdInput.tsx    # Player ID input field
│   │   └── PurchaseButton.tsx   # Purchase action button
│   ├── context/                 # React Context providers
│   │   └── UserContext.tsx      # User state management
│   ├── profile/                 # User profile pages
│   ├── wallet/                  # Digital wallet system
│   ├── globals.css              # Global styles
│   ├── layout.tsx               # Root layout
│   └── page.tsx                 # Homepage
├── components/                   # UI Components
│   ├── ui/                      # Radix UI components
│   └── theme-provider.tsx       # Theme management
├── hooks/                       # Custom React hooks
├── lib/                         # Utility functions
├── public/                      # Static assets
├── styles/                      # Additional styles
└── types/                       # TypeScript definitions
```

---

## 🎮 Gaming Products

### 💎 **Gems & Currencies**
- 110 Gems - $2.25
- 341 Gems - $6.75  
- 571 Gems - $11.25
- 1166 Gems - $22.50
- 2398 Gems - $45.00

### 👑 **Memberships**
- Weekly Membership - $4.60
- Monthly Membership - $16.00

### 🎫 **Battle Passes**
- Royale Pass - $6.90

---

## 👨‍💼 Admin Features

### 📊 **Dashboard Overview**
- Real-time system statistics
- User activity monitoring
- Order processing metrics
- System health status

### 👥 **User Management**
- User registration approval
- Balance management
- Account status control
- User activity tracking

### 📦 **Order Management**
- Order processing workflow
- Status updates (Pending → Completed → Cancelled)
- Transaction history
- Refund processing

### 🛍️ **Product Management**
- Gaming product catalog
- Price management
- Inventory tracking
- Product categorization

### ⚙️ **System Settings**
- Site configuration
- Payment settings
- Notification preferences
- Security options

---

## 🎨 Design System

### 🎨 **Color Palette**
```css
Primary: #8B2635 (Tesla Red)
Accent: #FF4C4C (Bright Red)
Background: #111111 (Dark)
Surface: #2a2a2a (Gray-800)
Text: #ffffff (White)
```

### 🔤 **Typography**
- **Font Family**: Cairo (Arabic + Latin support)
- **Weights**: 200-1000 variable font
- **Direction**: RTL (Right-to-Left) for Arabic

### 📱 **Responsive Breakpoints**
- Mobile: 640px
- Tablet: 768px  
- Desktop: 1024px
- Large: 1280px

---

## 🚀 Deployment

### Vercel (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel
```

### Manual Build
```bash
# Build for production
npm run build

# Start production server
npm start
```

---

## 🛠️ Development

### Available Scripts
```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run start    # Start production server
npm run lint     # Run ESLint
```

### Environment Variables
```env
# Add your environment variables here
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

---

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 👨‍💻 Author

**altyb**
- GitHub: [@altyb](https://github.com/altyb)
- Email: <EMAIL>

---

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - React framework
- [Radix UI](https://www.radix-ui.com/) - UI components
- [Tailwind CSS](https://tailwindcss.com/) - CSS framework
- [Lucide React](https://lucide.dev/) - Icon library
- [Cairo Font](https://fonts.google.com/specimen/Cairo) - Arabic typography

---

<div align="center">

**⭐ Star this repository if you found it helpful!**

Made with ❤️ for the gaming community

</div>
