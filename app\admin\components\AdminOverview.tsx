"use client"

import { TrendingUp, Clock, CheckCircle, AlertCircle } from "lucide-react"
import { useUser } from "../../context/UserContext"

export default function AdminOverview() {
  const { users, orders, pendingRegistrations } = useUser()

  const recentActivity = [
    { action: "طلب جديد", user: "أحمد محمد", time: "منذ 5 دقائق", type: "order" },
    { action: "تسجيل مستخدم", user: "فاطمة علي", time: "منذ 15 دقيقة", type: "registration" },
    { action: "دفعة مكتملة", user: "محمد سالم", time: "منذ 30 دقيقة", type: "payment" },
    { action: "استرداد طلب", user: "نورا أحمد", time: "منذ ساعة", type: "refund" },
  ]

  const systemStatus = [
    { name: "الخادم", status: "متصل", color: "green" },
    { name: "قاعدة البيانات", status: "متصلة", color: "green" },
    { name: "API", status: "يعمل", color: "green" },
    { name: "المدفوعات", status: "متاح", color: "green" },
  ]

  return (
    <div className="space-y-6">
      <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
        <h2 className="text-xl font-bold text-white mb-6">نظرة عامة على النظام</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-gray-900 border border-gray-600 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <TrendingUp className="w-8 h-8 text-green-400" />
              <div>
                <div className="text-2xl font-bold text-white">{users.length}</div>
                <div className="text-gray-400 text-sm">المستخدمين النشطين</div>
              </div>
            </div>
          </div>

          <div className="bg-gray-900 border border-gray-600 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <Clock className="w-8 h-8 text-yellow-400" />
              <div>
                <div className="text-2xl font-bold text-white">{pendingRegistrations.length}</div>
                <div className="text-gray-400 text-sm">طلبات الانتظار</div>
              </div>
            </div>
          </div>

          <div className="bg-gray-900 border border-gray-600 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="w-8 h-8 text-green-400" />
              <div>
                <div className="text-2xl font-bold text-white">
                  {orders.filter((o) => o.status === "completed").length}
                </div>
                <div className="text-gray-400 text-sm">طلبات مكتملة</div>
              </div>
            </div>
          </div>

          <div className="bg-gray-900 border border-gray-600 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <AlertCircle className="w-8 h-8 text-red-400" />
              <div>
                <div className="text-2xl font-bold text-white">
                  {orders.filter((o) => o.status === "pending").length}
                </div>
                <div className="text-gray-400 text-sm">طلبات معلقة</div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Activity */}
          <div>
            <h3 className="text-lg font-bold text-white mb-4">النشاط الأخير</h3>
            <div className="space-y-3">
              {recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center justify-between py-2 border-b border-gray-600">
                  <div>
                    <div className="text-white text-sm font-medium">{activity.action}</div>
                    <div className="text-gray-400 text-xs">{activity.user}</div>
                  </div>
                  <div className="text-gray-400 text-xs">{activity.time}</div>
                </div>
              ))}
            </div>
          </div>

          {/* System Status */}
          <div>
            <h3 className="text-lg font-bold text-white mb-4">حالة النظام</h3>
            <div className="space-y-3">
              {systemStatus.map((system, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-gray-300">{system.name}</span>
                  <span className={`text-${system.color}-400 text-sm`}>{system.status}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
