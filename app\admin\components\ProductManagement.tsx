"use client"

import { useState } from "react"
import { Plus, Edit, Trash2, Package } from "lucide-react"

interface Product {
  id: string
  name: string
  category: "gems" | "membership" | "pass"
  price: number
  description: string
  isActive: boolean
  createdAt: string
}

export default function ProductManagement() {
  const [products, setProducts] = useState<Product[]>([
    {
      id: "1",
      name: "💎 110 جوهرة",
      category: "gems",
      price: 2.25,
      description: "حزمة جواهر صغيرة",
      isActive: true,
      createdAt: "2024-01-01",
    },
    {
      id: "2",
      name: "💎 341 جوهرة",
      category: "gems",
      price: 6.75,
      description: "حزمة جواهر متوسطة",
      isActive: true,
      createdAt: "2024-01-01",
    },
    {
      id: "3",
      name: "💎 571 جوهرة",
      category: "gems",
      price: 11.25,
      description: "حزمة جواهر كبيرة",
      isActive: true,
      createdAt: "2024-01-01",
    },
    {
      id: "4",
      name: "عضوية شهرية",
      category: "membership",
      price: 16.0,
      description: "عضوية لمدة شهر واحد",
      isActive: true,
      createdAt: "2024-01-01",
    },
    {
      id: "5",
      name: "بوياه باس",
      category: "pass",
      price: 6.9,
      description: "تمرير موسمي",
      isActive: true,
      createdAt: "2024-01-01",
    },
  ])

  const [showAddForm, setShowAddForm] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    category: "gems" as Product["category"],
    price: "",
    description: "",
  })

  const handleAddProduct = () => {
    if (!formData.name || !formData.price) return

    const newProduct: Product = {
      id: Date.now().toString(),
      name: formData.name,
      category: formData.category,
      price: Number.parseFloat(formData.price),
      description: formData.description,
      isActive: true,
      createdAt: new Date().toISOString().split("T")[0],
    }

    setProducts([...products, newProduct])
    setFormData({ name: "", category: "gems", price: "", description: "" })
    setShowAddForm(false)
  }

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product)
    setFormData({
      name: product.name,
      category: product.category,
      price: product.price.toString(),
      description: product.description,
    })
    setShowAddForm(true)
  }

  const handleUpdateProduct = () => {
    if (!editingProduct || !formData.name || !formData.price) return

    const updatedProducts = products.map((p) =>
      p.id === editingProduct.id
        ? {
            ...p,
            name: formData.name,
            category: formData.category,
            price: Number.parseFloat(formData.price),
            description: formData.description,
          }
        : p,
    )

    setProducts(updatedProducts)
    setEditingProduct(null)
    setFormData({ name: "", category: "gems", price: "", description: "" })
    setShowAddForm(false)
  }

  const handleDeleteProduct = (productId: string) => {
    if (confirm("هل أنت متأكد من حذف هذا المنتج؟")) {
      setProducts(products.filter((p) => p.id !== productId))
    }
  }

  const toggleProductStatus = (productId: string) => {
    setProducts(products.map((p) => (p.id === productId ? { ...p, isActive: !p.isActive } : p)))
  }

  const getCategoryName = (category: string) => {
    switch (category) {
      case "gems":
        return "جواهر"
      case "membership":
        return "عضوية"
      case "pass":
        return "تمرير"
      default:
        return "غير محدد"
    }
  }

  return (
    <div className="space-y-6">
      <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white">إدارة المنتجات</h2>
          <button
            onClick={() => setShowAddForm(true)}
            className="bg-red-800 hover:bg-red-900 text-white px-4 py-2 rounded-lg transition-colors flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            إضافة منتج جديد
          </button>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {products.map((product) => (
            <div key={product.id} className="bg-gray-900 border border-gray-600 rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Package className="w-5 h-5 text-red-400" />
                  <span
                    className={`px-2 py-1 rounded text-xs ${
                      product.isActive ? "bg-green-500/20 text-green-400" : "bg-red-500/20 text-red-400"
                    }`}
                  >
                    {product.isActive ? "نشط" : "معطل"}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <button
                    onClick={() => handleEditProduct(product)}
                    className="p-1 hover:bg-gray-700 rounded transition-colors"
                    title="تعديل"
                  >
                    <Edit className="w-4 h-4 text-gray-400" />
                  </button>
                  <button
                    onClick={() => handleDeleteProduct(product.id)}
                    className="p-1 hover:bg-gray-700 rounded transition-colors"
                    title="حذف"
                  >
                    <Trash2 className="w-4 h-4 text-red-400" />
                  </button>
                </div>
              </div>

              <h3 className="text-white font-semibold mb-2">{product.name}</h3>
              <p className="text-gray-400 text-sm mb-2">{product.description}</p>

              <div className="flex items-center justify-between mb-3">
                <span className="text-red-400 font-bold">${product.price}</span>
                <span className="text-gray-400 text-sm">{getCategoryName(product.category)}</span>
              </div>

              <button
                onClick={() => toggleProductStatus(product.id)}
                className={`w-full py-2 px-3 rounded text-sm transition-colors ${
                  product.isActive
                    ? "bg-red-600 hover:bg-red-700 text-white"
                    : "bg-green-600 hover:bg-green-700 text-white"
                }`}
              >
                {product.isActive ? "تعطيل المنتج" : "تفعيل المنتج"}
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Add/Edit Product Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 border border-gray-600 rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-bold text-white mb-4">{editingProduct ? "تعديل المنتج" : "إضافة منتج جديد"}</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">اسم المنتج</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-800"
                  placeholder="مثال: 💎 110 جوهرة"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">الفئة</label>
                <select
                  value={formData.category}
                  onChange={(e) => setFormData({ ...formData, category: e.target.value as Product["category"] })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-800"
                >
                  <option value="gems">جواهر</option>
                  <option value="membership">عضوية</option>
                  <option value="pass">تمرير</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">السعر ($)</label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.price}
                  onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-800"
                  placeholder="0.00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">الوصف</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-800"
                  rows={3}
                  placeholder="وصف المنتج..."
                />
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={() => {
                  setShowAddForm(false)
                  setEditingProduct(null)
                  setFormData({ name: "", category: "gems", price: "", description: "" })
                }}
                className="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={editingProduct ? handleUpdateProduct : handleAddProduct}
                className="flex-1 bg-red-800 hover:bg-red-900 text-white py-2 px-4 rounded transition-colors"
              >
                {editingProduct ? "تحديث" : "إضافة"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
