"use client"

import { useState } from "react"
import { Edit, Check, X, Search } from "lucide-react"
import { useUser } from "../../context/UserContext"

export default function UserManagement() {
  const { users, pendingRegistrations, updateUserBalance, approveUser, rejectUser } = useUser()
  const [editingUser, setEditingUser] = useState<string | null>(null)
  const [newBalance, setNewBalance] = useState<string>("")
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<"all" | "active" | "suspended">("all")

  const handleEditBalance = (userId: string, currentBalance: number) => {
    setEditingUser(userId)
    setNewBalance(currentBalance.toString())
  }

  const handleSaveBalance = (userId: string) => {
    const balance = Number.parseFloat(newBalance)
    if (!isNaN(balance) && balance >= 0) {
      updateUserBalance(userId, balance)
      setEditingUser(null)
      setNewBalance("")
    }
  }

  const handleCancelEdit = () => {
    setEditingUser(null)
    setNewBalance("")
  }

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || user.status === statusFilter
    return matchesSearch && matchesStatus
  })

  return (
    <div className="space-y-6">
      {/* Pending Registrations */}
      {pendingRegistrations.length > 0 && (
        <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">طلبات التسجيل المعلقة</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {pendingRegistrations.map((user) => (
              <div key={user.id} className="bg-gray-900 border border-gray-600 rounded-lg p-4">
                <div className="mb-3">
                  <div className="text-white font-medium">{user.name}</div>
                  <div className="text-gray-400 text-sm">{user.email}</div>
                  <div className="text-gray-500 text-xs">ID: {user.id}</div>
                  <div className="text-gray-500 text-xs">تاريخ التسجيل: {user.createdAt}</div>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => approveUser(user.id)}
                    className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded text-sm transition-colors"
                  >
                    قبول
                  </button>
                  <button
                    onClick={() => rejectUser(user.id)}
                    className="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-3 rounded text-sm transition-colors"
                  >
                    رفض
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* User Management */}
      <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
          <h2 className="text-xl font-bold text-white">إدارة المستخدمين</h2>

          {/* Search and Filter */}
          <div className="flex flex-col md:flex-row gap-3">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث عن مستخدم..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-4 pr-10 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-red-800"
              />
            </div>

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as "all" | "active" | "suspended")}
              className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-red-800"
            >
              <option value="all">جميع الحالات</option>
              <option value="active">نشط</option>
              <option value="suspended">معلق</option>
            </select>
          </div>
        </div>

        <div className="space-y-4">
          {filteredUsers.map((user) => (
            <div key={user.id} className="bg-gray-900 border border-gray-600 rounded-lg p-4">
              <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-red-800 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-semibold text-lg">{user.name.charAt(0)}</span>
                  </div>
                  <div className="min-w-0">
                    <h3 className="text-white font-medium">{user.name}</h3>
                    <p className="text-gray-400 text-sm truncate">{user.email}</p>
                    <p className="text-gray-500 text-xs">ID: {user.id}</p>
                    <p className="text-gray-500 text-xs">تاريخ التسجيل: {user.createdAt}</p>
                  </div>
                </div>

                <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
                  <div className="flex items-center gap-4">
                    <div className="text-center">
                      {editingUser === user.id ? (
                        <div className="flex items-center gap-2">
                          <input
                            type="number"
                            value={newBalance}
                            onChange={(e) => setNewBalance(e.target.value)}
                            className="w-24 px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                            step="0.01"
                            min="0"
                            placeholder="الرصيد"
                          />
                          <button
                            onClick={() => handleSaveBalance(user.id)}
                            className="p-1 bg-green-600 hover:bg-green-700 rounded transition-colors"
                          >
                            <Check className="w-4 h-4 text-white" />
                          </button>
                          <button
                            onClick={handleCancelEdit}
                            className="p-1 bg-red-600 hover:bg-red-700 rounded transition-colors"
                          >
                            <X className="w-4 h-4 text-white" />
                          </button>
                        </div>
                      ) : (
                        <>
                          <div className="text-white font-semibold">${user.balance.toFixed(2)}</div>
                          <div className="text-gray-400 text-xs">الرصيد</div>
                        </>
                      )}
                    </div>

                    <div className="text-center">
                      <span
                        className={`px-3 py-1 rounded-full text-xs font-medium ${
                          user.status === "active" ? "bg-green-500/20 text-green-400" : "bg-red-500/20 text-red-400"
                        }`}
                      >
                        {user.status === "active" ? "نشط" : "معلق"}
                      </span>
                      <div className="text-gray-400 text-xs mt-1">{user.role}</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => handleEditBalance(user.id, user.balance)}
                      className="p-2 hover:bg-gray-700 rounded transition-colors"
                      disabled={editingUser === user.id}
                      title="تعديل الرصيد"
                    >
                      <Edit className="w-4 h-4 text-gray-400" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredUsers.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-400">لا توجد نتائج للبحث</p>
          </div>
        )}
      </div>
    </div>
  )
}
