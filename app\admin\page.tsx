"use client"

import { useState, useEffect, Suspense } from "react"
import { Users, DollarSign, ShoppingCart, Settings, Package, BarChart3, FileText, Menu, X, Plus, CheckCircle, TrendingUp, Bell, AlertTriangle, Home } from "lucide-react"
import { useUser } from "../context/UserContext"
import { useRouter, useSearchParams } from "next/navigation"

// Direct imports
import AdminOverview from "./components/AdminOverview"
import UserManagement from "./components/UserManagement"
import OrderManagement from "./components/OrderManagement"
import ProductManagement from "./components/ProductManagement"
import SystemSettings from "./components/SystemSettings"

type AdminSection = "overview" | "users" | "orders" | "products" | "settings"

// Simple loading state
function LoadingState() {
  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-red-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-white text-lg">جاري تحميل لوحة التحكم...</p>
      </div>
    </div>
  )
}

// Access denied state
function AccessDenied() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to home after 3 seconds
    const timer = setTimeout(() => {
      router.push('/')
    }, 3000)

    return () => clearTimeout(timer)
  }, [router])

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="text-center max-w-md mx-auto p-8">
        <AlertTriangle className="w-20 h-20 text-red-500 mx-auto mb-6" />
        <h1 className="text-2xl font-bold text-white mb-4">غير مسموح بالوصول</h1>
        <p className="text-gray-400 mb-6">
          ليس لديك صلاحيات للوصول إلى لوحة التحكم الإدارية
        </p>
        <div className="space-y-3">
          <button
            onClick={() => router.push('/')}
            className="w-full bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg transition-colors flex items-center justify-center gap-2"
          >
            <Home className="w-5 h-5" />
            العودة للصفحة الرئيسية
          </button>
          <p className="text-gray-500 text-sm">
            سيتم توجيهك تلقائياً خلال 3 ثوانٍ...
          </p>
        </div>
      </div>
    </div>
  )
}

function AdminDashboardContent() {
  const router = useRouter()
  const searchParams = useSearchParams()

  // Get section from URL params, default to overview
  const urlSection = searchParams.get('section') as AdminSection
  const [activeSection, setActiveSection] = useState<AdminSection>(urlSection || "overview")
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)
  const [showQuickActions, setShowQuickActions] = useState(false)
  const { users, orders, pendingRegistrations } = useUser()

  const totalUsers = users.length
  const totalSales = orders.reduce((sum, order) => (order.status === "completed" ? sum + order.price : sum), 0)
  const totalOrders = orders.length

  // Update URL when section changes
  const updateSection = (section: AdminSection) => {
    setActiveSection(section)
    const newSearchParams = new URLSearchParams(searchParams.toString())
    newSearchParams.set('section', section)

    // Preserve other params (like orderId, status, etc.)
    router.push(`/admin?${newSearchParams.toString()}`, { scroll: false })
  }

  // Initialize section from URL on component mount
  useEffect(() => {
    const urlSection = searchParams.get('section') as AdminSection
    if (urlSection && ['overview', 'users', 'orders', 'products', 'settings'].includes(urlSection)) {
      setActiveSection(urlSection)
    }
  }, [searchParams])

  // Close sidebar on escape key
  useEffect(() => {
    // Only run on client side to prevent hydration issues
    if (typeof window === 'undefined') return

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isMobileSidebarOpen) {
        setIsMobileSidebarOpen(false)
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isMobileSidebarOpen])

  // Prevent body scroll when sidebar is open
  useEffect(() => {
    // Only run on client side to prevent hydration issues
    if (typeof window === 'undefined') return

    if (isMobileSidebarOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isMobileSidebarOpen])

  const navigationItems = [
    { id: "overview", label: "نظرة عامة", icon: BarChart3, color: "bg-blue-600", shortLabel: "عامة" },
    { id: "users", label: "إدارة المستخدمين", icon: Users, color: "bg-green-600", shortLabel: "المستخدمين" },
    { id: "orders", label: "إدارة الطلبات", icon: ShoppingCart, color: "bg-purple-600", shortLabel: "الطلبات" },
    { id: "products", label: "إدارة المنتجات", icon: Package, color: "bg-orange-600", shortLabel: "المنتجات" },
    { id: "settings", label: "إعدادات النظام", icon: Settings, color: "bg-gray-600", shortLabel: "الإعدادات" },
  ]

  const quickActions = [
    { id: "add-product", label: "إضافة منتج", icon: Plus, action: () => updateSection("products") },
    { id: "approve-users", label: "موافقة المستخدمين", icon: CheckCircle, action: () => updateSection("users") },
    { id: "view-orders", label: "عرض الطلبات", icon: ShoppingCart, action: () => updateSection("orders") },
    { id: "settings", label: "الإعدادات", icon: Settings, action: () => updateSection("settings") },
  ]

  const renderActiveSection = () => {
    switch (activeSection) {
      case "overview":
        return <AdminOverview />
      case "users":
        return <UserManagement />
      case "orders":
        return <OrderManagement />
      case "products":
        return <ProductManagement />
      case "settings":
        return <SystemSettings />
      default:
        return <AdminOverview />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 pt-16">
      {/* Admin Header - Replaces main navbar */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-sm border-b border-gray-700">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">🔥</span>
            </div>
            <div>
              <h1 className="text-white font-bold text-lg">لوحة التحكم الإدارية</h1>
              <p className="text-gray-400 text-xs">تسلا شوب</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Back to Main Site - Desktop */}
            <a
              href="/"
              className="hidden md:flex items-center gap-2 px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors text-sm text-white"
            >
              <Home className="w-4 h-4" />
              العودة للموقع
            </a>

            {/* Back to Main Site - Mobile */}
            <a
              href="/"
              className="md:hidden w-10 h-10 bg-gray-700 hover:bg-gray-600 rounded-lg flex items-center justify-center transition-colors"
              title="العودة للموقع الرئيسي"
            >
              <Home className="w-5 h-5 text-white" />
            </a>

            {/* Quick Actions Button - Mobile */}
            <button
              onClick={() => setShowQuickActions(!showQuickActions)}
              className="md:hidden w-10 h-10 bg-red-600 hover:bg-red-700 rounded-lg flex items-center justify-center transition-colors"
              title="إجراءات سريعة"
            >
              <Plus className="w-5 h-5 text-white" />
            </button>

            {/* Mobile Sidebar Toggle */}
            <button
              onClick={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
              className="md:hidden w-10 h-10 bg-gray-700 hover:bg-gray-600 rounded-lg flex items-center justify-center transition-colors"
              title="قائمة التحكم"
            >
              {isMobileSidebarOpen ? <X className="w-5 h-5 text-white" /> : <Menu className="w-5 h-5 text-white" />}
            </button>
          </div>
        </div>
      </div>

      <div className="w-full px-3 py-3">
        {/* Quick Actions Dropdown - Mobile */}
        {showQuickActions && (
          <div className="md:hidden mb-4 bg-gray-800 border border-gray-600 rounded-xl p-4 shadow-xl">
            <h3 className="text-white font-semibold mb-3 text-center">إجراءات سريعة</h3>

            {/* Back to Website - Quick Action */}
            <div className="mb-4 pb-3 border-b border-gray-600">
              <a
                href="/"
                className="flex items-center justify-center gap-2 p-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors text-white"
              >
                <Home className="w-5 h-5" />
                <span className="text-sm font-medium">العودة للموقع الرئيسي</span>
              </a>
            </div>

            <div className="grid grid-cols-2 gap-3">
              {quickActions.map((action) => {
                const Icon = action.icon
                return (
                  <button
                    key={action.id}
                    onClick={action.action}
                    className="flex flex-col items-center gap-2 p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                  >
                    <Icon className="w-6 h-6 text-red-400" />
                    <span className="text-white text-xs text-center">{action.label}</span>
                  </button>
                )
              })}
            </div>
          </div>
        )}

        <div className="flex gap-4 lg:gap-6">
          {/* Desktop Sidebar - Narrower */}
          <div className="hidden lg:block w-64 flex-shrink-0">
            <div className="bg-gray-800 border border-gray-600 rounded-xl p-4 sticky top-24">
              <h2 className="text-white font-bold text-lg mb-4 text-center">قائمة التحكم</h2>

              {/* Back to Website - Desktop Sidebar */}
              <div className="mb-4 pb-3 border-b border-gray-600">
                <a
                  href="/"
                  className="w-full flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-all duration-200 text-white text-sm"
                >
                  <Home className="w-4 h-4" />
                  <span className="font-medium">العودة للموقع</span>
                </a>
              </div>

              <nav className="space-y-2">
                {navigationItems.map((item) => {
                  const Icon = item.icon
                  return (
                    <button
                      key={item.id}
                      onClick={() => updateSection(item.id as AdminSection)}
                      className={`w-full flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 text-sm ${
                        activeSection === item.id
                          ? `${item.color} text-white shadow-lg`
                          : "text-gray-300 hover:bg-gray-700 hover:text-white"
                      }`}
                    >
                      <Icon className="w-4 h-4" />
                      <span className="font-medium">{item.shortLabel}</span>
                    </button>
                  )
                })}
              </nav>
            </div>
          </div>

          {/* Mobile Sidebar Overlay */}
          {isMobileSidebarOpen && (
            <div className="lg:hidden fixed inset-0 z-40 bg-black bg-opacity-50" onClick={() => setIsMobileSidebarOpen(false)}>
              <div className="fixed top-0 right-0 h-full w-80 bg-gray-800 border-l border-gray-600 shadow-xl transform transition-transform duration-300 ease-in-out">
                <div className="p-6 pt-20">
                  <h2 className="text-white font-bold text-lg mb-6 text-center">قائمة التحكم</h2>

                  {/* Back to Website - Mobile Sidebar */}
                  <div className="mb-6 pb-4 border-b border-gray-600">
                    <a
                      href="/"
                      className="w-full flex items-center gap-3 px-4 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-all duration-200 text-white"
                    >
                      <Home className="w-5 h-5" />
                      <span className="font-medium">العودة للموقع الرئيسي</span>
                    </a>
                  </div>

                  <nav className="space-y-3">
                    {navigationItems.map((item) => {
                      const Icon = item.icon
                      return (
                        <button
                          key={item.id}
                          onClick={() => {
                            updateSection(item.id as AdminSection)
                            setIsMobileSidebarOpen(false)
                          }}
                          className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                            activeSection === item.id
                              ? `${item.color} text-white shadow-lg`
                              : "text-gray-300 hover:bg-gray-700 hover:text-white"
                          }`}
                        >
                          <Icon className="w-5 h-5" />
                          <span className="font-medium">{item.label}</span>
                        </button>
                      )
                    })}
                  </nav>
                </div>
              </div>
            </div>
          )}

          {/* Main Content - Expanded */}
          <div className="flex-1 min-w-0">
            <div className="bg-gray-800 border border-gray-600 rounded-xl min-h-[calc(100vh-8rem)] w-full">
              {renderActiveSection()}
            </div>
          </div>
        </div>
              </div>
      </div>
    </div>
  )
}

function AdminDashboard() {
  const { users } = useUser()

  // For now, we'll assume admin access is available
  // In a real app, you'd check user permissions here
  return <AdminDashboardContent />
}

// Loading fallback for Suspense
function AdminPageLoading() {
  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-red-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-white text-lg">جاري تحميل لوحة التحكم...</p>
      </div>
    </div>
  )
}

// Wrapped export with Suspense for useSearchParams
export default function AdminPage() {
  return (
    <Suspense fallback={<AdminPageLoading />}>
      <AdminDashboard />
    </Suspense>
  )
}
