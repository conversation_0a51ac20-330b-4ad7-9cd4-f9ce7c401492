"use client"

import { useState } from "react"
import { Menu, X, Wallet } from "lucide-react"
import Image from "next/image"
import { useUser } from "../context/UserContext"
import CopyButton from "./CopyButton"

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { currentUser } = useUser()

  return (
    <nav className="border-b border-gray-700" style={{ backgroundColor: "#1A1A1A" }}>
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div
              className="w-10 h-10 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: "#8B2635" }}
            >
              <Image
                src="/placeholder.svg?height=32&width=32"
                alt="تسلا شوب - Tesla Shop"
                width={32}
                height={32}
                className="rounded"
              />
            </div>
            <div className="mr-3 hidden md:block">
              <div className="text-white font-bold text-lg">تسلا شوب</div>
              <div className="text-gray-400 text-xs">Tesla Shop</div>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-reverse space-x-8">
            <a href="/" className="text-white hover:text-red-400 transition-colors">
              الرئيسية
            </a>
            <a href="/profile" className="text-white hover:text-red-400 transition-colors">
              الملف الشخصي
            </a>
            <a href="/wallet" className="text-white hover:text-red-400 transition-colors">
              المحفظة
            </a>
            <a href="/admin" className="text-white hover:text-red-400 transition-colors">
              لوحة التحكم
            </a>
          </div>

          {/* Wallet Balance & User Info */}
          <div className="hidden md:flex items-center gap-4">
            {currentUser && (
              <>
                <div className="flex items-center gap-2 bg-gray-800 px-3 py-2 rounded-lg">
                  <Wallet className="w-4 h-4 text-red-400" />
                  <span className="text-white font-semibold">${currentUser.balance.toFixed(2)}</span>
                </div>
                <div className="text-white text-sm">
                  <div className="font-medium">{currentUser.name}</div>
                  <div className="text-gray-400 text-xs flex items-center gap-1">
                    <span>ID: {currentUser.id}</span>
                    <CopyButton text={currentUser.id} size="sm" />
                  </div>
                </div>
              </>
            )}
          </div>

          {/* Hamburger Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 rounded-lg hover:bg-gray-800 transition-colors"
            aria-label="فتح القائمة"
          >
            {isMenuOpen ? <X className="w-6 h-6 text-white" /> : <Menu className="w-6 h-6 text-white" />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-700 py-4">
            {currentUser && (
              <div className="px-4 py-2 mb-4 bg-gray-800 rounded-lg mx-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-white font-medium">{currentUser.name}</div>
                    <div className="text-gray-400 text-xs flex items-center gap-1">
                      <span>ID: {currentUser.id}</span>
                      <CopyButton text={currentUser.id} size="sm" />
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Wallet className="w-4 h-4 text-red-400" />
                    <span className="text-white font-semibold">${currentUser.balance.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            )}
            <div className="space-y-2">
              <a href="/" className="block px-4 py-2 text-white hover:bg-gray-800 rounded-lg transition-colors">
                الرئيسية
              </a>
              <a href="/profile" className="block px-4 py-2 text-white hover:bg-gray-800 rounded-lg transition-colors">
                الملف الشخصي
              </a>
              <a href="/wallet" className="block px-4 py-2 text-white hover:bg-gray-800 rounded-lg transition-colors">
                المحفظة
              </a>
              <a href="/admin" className="block px-4 py-2 text-white hover:bg-gray-800 rounded-lg transition-colors">
                لوحة التحكم
              </a>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
