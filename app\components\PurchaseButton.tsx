"use client"

interface PurchaseButtonProps {
  onPurchase: () => void
  disabled: boolean
}

export default function PurchaseButton({ onPurchase, disabled }: PurchaseButtonProps) {
  return (
    <button
      onClick={onPurchase}
      disabled={disabled}
      className={`text-white font-bold py-4 px-8 rounded-lg text-xl transition-all duration-200 hover:scale-105 shadow-lg min-w-[200px] ${
        disabled ? "opacity-50 cursor-not-allowed" : "hover:shadow-red-500/20"
      }`}
      style={{
        backgroundColor: disabled ? "#666" : "#FF4C4C",
      }}
      onMouseEnter={(e) => {
        if (!disabled) {
          e.currentTarget.style.backgroundColor = "#e53e3e"
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled) {
          e.currentTarget.style.backgroundColor = "#FF4C4C"
        }
      }}
    >
      شحن الآن
    </button>
  )
}
