import type React from "react"
import type { Metada<PERSON> } from "next"
import "./globals.css"
import Navbar from "./components/Navbar"
import { UserProvider } from "./context/UserContext"

export const metadata: Metadata = {
  title: "تسلا شوب - شحن بطاقات الألعاب والتطبيقات | Tesla Shop",
  description:
    "تسلا شوب - أفضل موقع لشحن بطاقات الألعاب والتطبيقات. شحن فوري وآمن لجميع الألعاب مثل PUBG, Free Fire, Fortnite وأكثر. أسعار منافسة وخدمة عملاء ممتازة.",
  keywords: [
    "تسلا شوب",
    "tesla shop",
    "teslashop",
    "شحن ألعاب",
    "بطاقات ألعاب",
    "شحن PUBG",
    "شحن Free Fire",
    "شحن Fortnite",
    "جواهر PUBG",
    "عضويات ألعاب",
    "شحن فوري",
    "بطاقات تطبيقات",
    "شحن آمن",
    "gaming cards",
    "game top up",
    "mobile gaming",
    "gaming credits",
    "digital cards",
    "instant delivery",
    "gaming store",
    "online gaming",
    "mobile games",
    "gaming platform",
  ].join(", "),
  authors: [{ name: "Tesla Shop", url: "https://teslashop.com" }],
  creator: "Tesla Shop",
  publisher: "Tesla Shop",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "ar_SA",
    alternateLocale: ["en_US"],
    url: "https://teslashop.com",
    siteName: "تسلا شوب - Tesla Shop",
    title: "تسلا شوب - شحن بطاقات الألعاب والتطبيقات | Tesla Shop",
    description:
      "تسلا شوب - أفضل موقع لشحن بطاقات الألعاب والتطبيقات. شحن فوري وآمن لجميع الألعاب مثل PUBG, Free Fire, Fortnite وأكثر.",
    images: [
      {
        url: "https://teslashop.com/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "تسلا شوب - شحن بطاقات الألعاب",
        type: "image/jpeg",
      },
      {
        url: "https://teslashop.com/og-image-square.jpg",
        width: 1200,
        height: 1200,
        alt: "تسلا شوب - شحن بطاقات الألعاب",
        type: "image/jpeg",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "@teslashop",
    creator: "@teslashop",
    title: "تسلا شوب - شحن بطاقات الألعاب والتطبيقات",
    description: "تسلا شوب - أفضل موقع لشحن بطاقات الألعاب والتطبيقات. شحن فوري وآمن لجميع الألعاب.",
    images: ["https://teslashop.com/twitter-image.jpg"],
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
    yahoo: "your-yahoo-verification-code",
  },
  alternates: {
    canonical: "https://teslashop.com",
    languages: {
      "ar-SA": "https://teslashop.com",
      "en-US": "https://teslashop.com/en",
    },
  },
  category: "Gaming",
  classification: "Gaming Cards and Digital Services",
  other: {
    "apple-mobile-web-app-title": "تسلا شوب",
    "application-name": "Tesla Shop",
    "msapplication-TileColor": "#8B2635",
    "theme-color": "#8B2635",
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" className="dark">
      <head>
        {/* Google Fonts - Cairo */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap" rel="stylesheet" />

        <link rel="canonical" href="https://teslashop.com" />
        <link rel="alternate" hrefLang="ar" href="https://teslashop.com" />
        <link rel="alternate" hrefLang="en" href="https://teslashop.com/en" />
        <link rel="alternate" hrefLang="x-default" href="https://teslashop.com" />

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              name: "تسلا شوب",
              alternateName: "Tesla Shop",
              url: "https://teslashop.com",
              logo: "https://teslashop.com/logo.png",
              description: "أفضل موقع لشحن بطاقات الألعاب والتطبيقات في الشرق الأوسط",
              sameAs: [
                "https://twitter.com/teslashop",
                "https://facebook.com/teslashop",
                "https://instagram.com/teslashop",
              ],
              contactPoint: {
                "@type": "ContactPoint",
                telephone: "+966501234567",
                contactType: "customer service",
                availableLanguage: ["Arabic", "English"],
              },
              areaServed: {
                "@type": "Country",
                name: "Saudi Arabia",
              },
              serviceType: "Gaming Cards and Digital Services",
            }),
          }}
        />

        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              name: "تسلا شوب",
              url: "https://teslashop.com",
              potentialAction: {
                "@type": "SearchAction",
                target: "https://teslashop.com/search?q={search_term_string}",
                "query-input": "required name=search_term_string",
              },
            }),
          }}
        />
      </head>
      <body className="cairo">
        <UserProvider>
          <Navbar />
          <main>{children}</main>
        </UserProvider>
      </body>
    </html>
  )
}
